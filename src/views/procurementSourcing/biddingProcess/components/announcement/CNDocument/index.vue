<template>
  <el-form
    ref="mainFormRef"
    :model="cnDocumentForm"
    :rules="formRules"
    label-width="150px"
    :disabled="isDisabled"
  >
    <div class="procurement-project-container">
      <!-- 左侧锚点导航 -->
      <div class="anchor-navigation" v-if="!hideAnchor">
        <ul class="nav-list">
          <!-- 高亮滑块 -->
          <div
            v-show="isSliderReady"
            class="nav-slider"
            :style="{
              height: sliderStyle.height,
              transform: sliderStyle.transform,
              top: '4px',
            }"
          ></div>

          <li
            v-for="anchor in anchorList"
            :key="anchor.id"
            class="nav-item"
            :class="{ active: activeAnchor === anchor.id }"
            @click="scrollToSection(anchor.id)"
          >
            {{ anchor.label }}
          </li>
        </ul>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 地址设置 -->
        <div
          :id="'section-' + 'address'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.address"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="address">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  地址设置
                </span>
              </template>
              <div class="form-content">
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item
                        label="文件递交地址"
                        prop="deliveryAddress"
                      >
                        <el-input
                          v-model="cnDocumentForm.deliveryAddress"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入文件递交地址"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 标书费和标书文件设置 -->
        <div
          :id="'section-' + 'bidFee'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.bidFee"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="bidFee">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  标书费和标书文件设置
                </span>
              </template>
              <div class="form-content">
                <!-- 全局标书费收取设置 -->
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item
                        label="标书费收取"
                        prop="bidFeeCollection"
                      >
                        <el-radio-group v-model="cnDocumentForm.bidFeeCollection">
                          <el-radio :label="true">收取标书费</el-radio>
                          <el-radio :label="false">不收标书费</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 标段选择 -->
                <div
                  class="flex"
                  v-if="lotList.length > 1"
                >
                  <div
                    class="px-5"
                    v-for="(item, index) in lotList"
                    :key="index"
                  >
                    <div
                      class="py-[9px] lot-item mb-4"
                      :class="{ active: activeBidFeeLotId === item.id }"
                      @click="handleBidFeeLotClick(item.id)"
                    >
                      {{ item.sectionName }}
                    </div>
                  </div>
                </div>
                <template v-if="getCurrentBidFeeData()">
                  <div class="section-form">

                    <!-- 标书费详细信息 -->
                    <template v-if="cnDocumentForm.bidFeeCollection === true">
                      <el-row :gutter="24">
                        <el-col :span="12">
                          <el-form-item
                            label="标书费金额"
                            required
                          >
                            <el-input
                              v-model="getCurrentBidFeeData().bidFeeAmount"
                              placeholder="请输入标书费金额"
                            >
                              <template #append>元</template>
                            </el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item
                            label="汇款银行账户"
                            required
                          >
                            <el-input
                              v-model="getCurrentBidFeeData().payAccount"
                              placeholder="请输入银行账户"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                      <el-row :gutter="24">
                        <el-col :span="12">
                          <el-form-item
                            label="汇款银行"
                            required
                          >
                            <el-input
                              v-model="getCurrentBidFeeData().payBank"
                              placeholder="请输入汇款银行"
                            />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item
                            label="汇款开户行"
                            prop="openAccountBank"
                          >
                            <el-input
                              v-model="getCurrentBidFeeData().openAccountBank"
                              placeholder="请输入开户行"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </template>

                    <el-row :gutter="24">
                      <el-col :span="24">
                        <el-form-item
                          label="标书文件"
                          prop="bidDocuments"
                        >
                          <div class="upload-section">
                            <YunUpload
                              v-model="lotBidDocumentFiles[activeBidFeeLotId]"
                              @change="handleLotBidDocumentUpload"
                              :key="`upload-${activeBidFeeLotId}`"
                              v-if="!isDisabled && cndDocumentInfo.docStatus !== 'APPROVE' && !hideAction && isPurchaser"
                            ></YunUpload>
                            <!-- 当不能编辑时，显示文件下载链接 -->
                            <div v-else-if="lotBidDocumentFiles[activeBidFeeLotId]?.length">
                              <el-link
                                icon="Document"
                                type="primary"
                                :href="file.url"
                                target="_blank"
                                :disabled="false"
                                v-for="(file, index) in lotBidDocumentFiles[activeBidFeeLotId] || []"
                                :key="index"
                                @click="handleFileDownload(file)"
                              >
                                {{ file.name }}
                              </el-link>
                            </div>
                          </div>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>
                </template>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 供应商报价须知 -->
        <div
          :id="'section-' + 'notice'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.notice"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="notice">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  供应商报价须知
                </span>
              </template>
              <div class="form-content">
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item
                        label="投标须知"
                        prop="quotationNotice"
                      >
                        <el-input
                          v-model="cnDocumentForm.quotationNotice"
                          type="textarea"
                          :rows="6"
                          placeholder="请输入投标须知内容"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 评审规则 -->
        <div
          :id="'section-' + 'evaluation'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.evaluation"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="evaluation">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  评审规则
                </span>
              </template>
              <div class="form-content">
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item
                        label="评审规则"
                        prop="evaluationMethod"
                      >
                        <el-radio-group v-model="cnDocumentForm.evaluationMethod">
                          <el-radio
                            v-for="option in evaluationRuleOptions"
                            :key="option.value"
                            :label="option.value"
                          >
                            {{ option.label }}
                          </el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>



        <!-- 采购时间要求 -->
        <div
          :id="'section-' + 'time'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.time"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="time">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  采购时间要求
                </span>
              </template>
              <div class="form-content">
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item
                        label="文件获取起止时间"
                        prop="timeRequirements.documentObtainTimeRange"
                      >
                        <el-date-picker
                          v-model="cnDocumentForm.timeRequirements.documentObtainTimeRange"
                          type="datetimerange"
                          range-separator="至"
                          :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                          start-placeholder="文件获取开始时间"
                          end-placeholder="文件获取结束时间"
                          style="width: 100%"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="标书费缴纳起止时间"
                        prop="timeRequirements.bidFeePaymentTimeRange"
                      >
                        <el-date-picker
                          v-model="cnDocumentForm.timeRequirements.bidFeePaymentTimeRange"
                          type="datetimerange"
                          range-separator="至"
                          :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                          start-placeholder="标书费缴纳开始时间"
                          end-placeholder="标书费缴纳结束时间"
                          style="width: 100%"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="24">
                    <el-col :span="12">
                      <el-form-item
                        label="报价起止时间"
                        prop="timeRequirements.quotationTimeRange"
                      >
                        <el-date-picker
                          v-model="cnDocumentForm.timeRequirements.quotationTimeRange"
                          type="datetimerange"
                          range-separator="至"
                          :defaultTime="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                          start-placeholder="报价开始时间"
                          end-placeholder="报价结束时间"
                          style="width: 100%"
                          format="YYYY-MM-DD HH:mm:ss"
                          value-format="YYYY-MM-DD HH:mm:ss"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 评分细则 -->
        <div
          :id="'section-' + 'scoring'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.scoring"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="scoring">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  评分细则
                </span>
              </template>
              <div class="form-content">
                <!-- 评分细则组件 -->
                <ScoringRules
                  v-model="cnDocumentForm.scoringRules"
                  v-model:active-lot-id="activeScoringLotId"
                  :lot-list="lotList"
                />
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 审批设置 -->
        <div
          :id="'section-' + 'approval'"
          class="content-section"
        >
          <el-collapse
            v-model="activeCollapse.approval"
            class="section-collapse"
            expand-icon-position="left"
          >
            <el-collapse-item name="approval">
              <template #title>
                <span class="collapse-title">
                  <el-icon class="collapse-icon"><CaretRight /></el-icon>
                  审批设置
                </span>
              </template>
              <div class="form-content">
                <div class="section-form">
                  <el-row :gutter="24">
                    <el-col :span="24">
                      <el-form-item
                        label="发起审批"
                        prop="approvalData.specialProcessExecutorList"
                      >
                        <div class="flex items-center gap-2">
                          <el-radio-group
                            v-model="cnDocumentForm.approvalData.approvalType"
                            @change="handleApprovalTypeChange"
                          >
                            <el-radio :label="0">系统自动发起</el-radio>
                            <el-radio :label="1">指定审批人</el-radio>
                          </el-radio-group>
                          <UserSelector
                            v-model="cnDocumentForm.approvalData.specialProcessExecutorList"
                            v-show="cnDocumentForm.approvalData.approvalType === 1"
                            :multiple="true"
                            style="width: auto;"
                            placeholder="请选择审批人"
                            @update:model-value="handleApprovalDataChange"
                          />
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 表单操作按钮 -->
    <div v-if="isPurchaser && !hideAction && cndDocumentInfo.docStatus !== 'APPROVE'" class="form-actions-wrapper">
      <div class="form-actions">
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="btnLoading"
        >
          保存竞谈文件
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </div>


  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch, defineProps, defineExpose } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'yun-design';
import { CaretRight } from '@element-plus/icons-vue';
import YunUpload from '@/components/YunUpload/index.vue';
import ScoringRules from './components/ScoringRules.vue';
import UserSelector from '@/components/UserSelector/index.vue';
import type {
  CNDocumentFormData,
  BidFeeInfo,
  LotInfo,
  EvaluationRuleOption,
  CNDocumentApiData,
  NegotiationDocApiData,
  BidsSegment,
  EvaluationStandardInfo
} from './types';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { useUserRole } from '@/views/procurementSourcing/biddingProcess/utils/useUserRole';
import {
  getCNDocumentByProjectId,
  getNegotiationDocDetail,
  upsertNegotiationDoc
} from './api';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import { cloneDeep } from 'lodash';
import { downloadedBidFileDownloadStatus } from '@/api/purchasing/evaluation';

const router = useRouter()
// 用户角色判断
const { isPurchaser } = useUserRole();
const props = defineProps({
  hideAction: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: ''
  },

  // 是否隐藏锚点导航 供端 需要设置的标段id
  hideAnchor: {
    type: Boolean,
    default: false
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  downloadSectionId: {
    type: String,
    default: ''
  }
});

// 下载文件
const handleFileDownload = (file: any) => {
  const { name, url } = file || {};
  if (name && url) {
    // 如果有 downloadSectionId，则调用下载状态接口
    if (props.downloadSectionId && noticeId.value) {
      downloadedBidFileDownloadStatus(noticeId.value, props.downloadSectionId);
    }
  } else {
    ElMessage.error('文件不存在');
  }
};

// 锚点导航相关
const anchorList = ref([
  { id: 'address', label: '地址设置' },
  { id: 'bidFee', label: '标书费和标书文件设置' },
  { id: 'notice', label: '供应商报价须知' },
  { id: 'evaluation', label: '评审规则' },
  { id: 'scoring', label: '评分细则' },
  { id: 'time', label: '采购时间要求' },
  { id: 'approval', label: '审批设置' },
]);

const activeAnchor = ref('address');
const sliderStyle = ref({ height: '16px', transform: 'translateY(0px)' });
const isSliderReady = ref(false);
const btnLoading = ref(false);

// 折叠面板控制
const activeCollapse = reactive({
  address: ['address'],
  bidFee: ['bidFee'],
  notice: ['notice'],
  evaluation: ['evaluation'],
  scoring: ['scoring'],
  time: ['time'],
  approval: ['approval'],
});

// 主表单引用
const mainFormRef = ref();
const route = useRoute()

// Store相关
const biddingStore = useBiddingStore();
const projectId = computed(() => biddingStore?.projectId);
const projectDetail = computed(() => biddingStore?.projectDetail);
const noticeId = computed(() => {
  return route.query.noticeId || biddingStore?.noticeId || biddingStore?.projectDetail?.tenderNotice?.id;
});

const cndDocumentInfo = computed(() => {
  return biddingStore.cndDocumentInfo || {}
})

// 标段相关
const lotList = ref<LotInfo[]>([]);
const activeBidFeeLotId = ref('');
const activeScoringLotId = ref('');

// 文件上传相关
const bidDocumentFiles = ref([]);
const lotBidDocumentFiles = ref<Record<string, any[]>>({});



// 评审规则选项
const evaluationRuleOptions = ref<EvaluationRuleOption[]>([
  { value: 'LOWEST_PRICE_AFTER_REVIEW', label: '经评审最低价法' },
  { value: 'LOWEST_PRICE', label: '最低价法' },
  { value: 'COMPREHENSIVE_EVALUATION', label: '综合评估法' },
  { value: 'HIGHEST_PRICE', label: '最高价法' },
]);

// 统一的API数据格式 - 用于初始化和提交
const negotiationDocData = reactive<NegotiationDocApiData>({
  projectId: 0,
  noticeId: 0,
  fileSubmissionAddress: '',
  bidFeeCollection: false,
  bidsSegments: [],
  biddingNotice: '',
  evaluationMethod: 'LOWEST_PRICE_AFTER_REVIEW',
  fileObtainStartTime: '',
  fileObtainEndTime: '',
  bidDocPayStartTime: '',
  bidDocPayEndTime: '',
  quoteStartTime: '',
  quoteEndTime: '',
  evaluationStandardInfos: [],
  approvalType: 0,
  specialProcessExecutorList: []
});

// 表单数据 - 保持原有逻辑和样式
const cnDocumentForm = reactive<CNDocumentFormData>({
  deliveryAddress: '',
  bidFeeCollection: false, // 全局标书费收取设置
  bidFeeSegments: [],
  bidDocuments: [],
  lotBidDocuments: [],
  quotationNotice: '',
  evaluationMethod: 'LOWEST_PRICE_AFTER_REVIEW',
  scoringRules: [], // 评分细则
  timeRequirements: {
    documentObtainTimeRange: ['', ''],
    bidFeePaymentTimeRange: ['', ''],
    quotationTimeRange: ['', ''],
  },
  approvalData: {
    approvalType: 0, // 0: 系统自动发起, 1: 指定审批人
    specialProcessExecutorList: [], // 指定审批人列表
  }
});

// 初始化标段数据
const initializeLotData = () => {
  // if(!noticeId.value) return

  lotList.value = projectDetail.value?.projectSectionList || [];

  // 设置默认选中的标段
  if (lotList.value.length > 0) {
    activeBidFeeLotId.value = lotList.value[0].id;
  }

  // 初始化标书费数据（移除bidFeeCollection，因为现在是全局设置）
  cnDocumentForm.bidFeeSegments = lotList.value.map(lot => ({
    lotId: lot.id,
    sectionName: lot.sectionName,
    bidFeeAmount: '',
    payAccount: '',
    payBank: '',
    openAccountBank: '',
  }));

  // 初始化按标段的标书文件数据
  cnDocumentForm.lotBidDocuments = lotList.value.map(lot => ({
    lotId: lot.id,
    bidDocuments: []
  }));

  // 初始化文件上传组件数据 - 确保每个标段都有独立的文件数组
  lotBidDocumentFiles.value = {};
  lotList.value.forEach(lot => {
    // 使用深拷贝确保每个标段的文件数组都是独立的
    lotBidDocumentFiles.value[lot.id] = [];
  });
  // 初始化评分细则数据
  cnDocumentForm.scoringRules = [];
};



// 表单校验规则
const formRules = computed(() => {
  const rules = {
    deliveryAddress: [
      { required: true, message: '请输入文件递交地址', trigger: 'blur' }
    ],
    quotationNotice: [
      { required: true, message: '请输入投标须知', trigger: 'blur' }
    ],
    evaluationMethod: [
      { required: true, message: '请选择评审规则', trigger: 'change' }
    ],
    'timeRequirements.documentObtainTimeRange': [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择文件获取起止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    'timeRequirements.quotationTimeRange': [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择报价起止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    bidDocuments: [
      {
        required: true,
        validator: (rule, value, callback) => {
          // 检查所有标段是否都上传了标书文件
          const missingLots = [];

          for (const lot of lotList.value) {
            const lotFiles = lotBidDocumentFiles.value[lot.id];
            if (!lotFiles || lotFiles.length === 0) {
              missingLots.push(lot.sectionName);
            }
          }

          if (missingLots.length > 0) {
            callback(new Error(`请为以下标段上传标书文件：${missingLots.join('、')}`));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
  };

  // 如果选择了收取标书费，则标书费缴纳时间为必填
  if (cnDocumentForm.bidFeeCollection === true) {
    rules['timeRequirements.bidFeePaymentTimeRange'] = [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择标书费缴纳起止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ];
  }

  // 审批相关验证规则 - 只在选择指定审批人时验证
  if (cnDocumentForm.approvalData?.approvalType === 1) {
    rules['approvalData.specialProcessExecutorList'] = [
      { required: true, message: '请选择审批人', trigger: 'change' }
    ];
  }

  return rules;
});

// 获取当前选中标段的标书费数据
const getCurrentBidFeeData = (): BidFeeInfo | undefined => {
  return cnDocumentForm.bidFeeSegments.find(segment => segment.lotId === activeBidFeeLotId.value);
};

// 标段点击处理
const handleBidFeeLotClick = (lotId: string) => {
  // 在切换标段前，先保存当前标段的文件数据
  const currentLotId = activeBidFeeLotId.value;
  if (currentLotId && lotBidDocumentFiles.value[currentLotId]) {
    // 确保当前标段的文件数据已经正确保存到表单数据中
    const lotDocIndex = cnDocumentForm.lotBidDocuments.findIndex(item => item.lotId === currentLotId);
    if (lotDocIndex !== -1) {
      const currentFiles = lotBidDocumentFiles.value[currentLotId] || [];
      cnDocumentForm.lotBidDocuments[lotDocIndex].bidDocuments = currentFiles.map(item => ({
        fileName: item.name,
        filePath: item.url
      }));
    }
  }

  // 切换到新标段
  activeBidFeeLotId.value = lotId;

  // 确保新标段的文件数据正确初始化
  if (!lotBidDocumentFiles.value[lotId]) {
    lotBidDocumentFiles.value[lotId] = [];
  }

  // 切换标段时触发标书文件校验
  nextTick(() => {
    if (mainFormRef.value) {
      mainFormRef.value.validateField('bidDocuments');
    }
  });
};





// 按标段的文件上传处理
const handleLotBidDocumentUpload = (fileList?: any[]) => {
  const currentLotId = activeBidFeeLotId.value;

  // 如果传入了fileList参数，使用传入的数据，否则使用当前标段的文件数据
  const files = fileList || lotBidDocumentFiles.value[currentLotId] || [];

  // 确保当前标段的文件数据是最新的
  if (fileList) {
    lotBidDocumentFiles.value[currentLotId] = [...fileList];
  }

  // 更新当前标段的文件数据到表单中
  const lotDocIndex = cnDocumentForm.lotBidDocuments.findIndex(item => item.lotId === currentLotId);
  if (lotDocIndex !== -1) {
    cnDocumentForm.lotBidDocuments[lotDocIndex].bidDocuments = files.map(item => ({
      fileName: item.name,
      filePath: item.url
    }));
  }

  console.log(`标段 ${currentLotId} 文件更新:`, files);
  console.log('所有标段文件数据:', lotBidDocumentFiles.value);

  // 触发标书文件字段的校验
  nextTick(() => {
    if (mainFormRef.value) {
      mainFormRef.value.validateField('bidDocuments');
    }
  });
};

// 锚点导航滚动
const scrollToSection = (sectionId: string) => {
  activeAnchor.value = sectionId;
  const element = document.getElementById(`section-${sectionId}`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }
  updateSliderPosition();
};

// 更新滑块位置
const updateSliderPosition = () => {
  nextTick(() => {
    const activeIndex = anchorList.value.findIndex(anchor => anchor.id === activeAnchor.value);
    if (activeIndex !== -1) {
      const itemHeight = 40; // nav-item 的高度
      const transform = `translateY(${activeIndex * itemHeight}px)`;
      sliderStyle.value = {
        height: '32px',
        transform,
      };
      isSliderReady.value = true;
    }
  });
};

// 数据转换：表单数据转新接口数据 - 恢复正确的组装逻辑
const formToNegotiationApiData = (): NegotiationDocApiData => {
  const timeReq = cnDocumentForm.timeRequirements;

  // 转换为 bidsSegments - 每个标段一个对象，包含标书文件和标书费信息
  const bidsSegments: BidsSegment[] = [];

  // 按标段处理，每个标段创建一个 BID_DOCUMENT 类型的对象
  lotList.value.forEach(lot => {
    const sectionId = lot.id;
    const lotDoc = cnDocumentForm.lotBidDocuments.find(doc => doc.lotId === lot.id);
    const feeSegment = cnDocumentForm.bidFeeSegments.find(seg => seg.lotId === lot.id);

    // 准备附件信息
    const attachmentInfos: any[] = [];
    if (lotDoc && lotDoc.bidDocuments && lotDoc.bidDocuments.length > 0) {
      lotDoc.bidDocuments.forEach(doc => {
        attachmentInfos.push({
          businessId: sectionId,
          businessType: 'BID_DOC',
          fileName: doc.fileName,
          filePath: doc.filePath,
          fileType: doc.fileName?.split('.').pop() || '',
          fileSize: doc.fileSize || 0
        });
      });
    }

    // 准备标书费信息
    let bidFeeInfo: any = undefined;
    if (cnDocumentForm.bidFeeCollection === true && feeSegment) {
      bidFeeInfo = {
        bidFee: feeSegment.bidFeeAmount,
        bankAccount: feeSegment.payAccount,
        bank: feeSegment.payBank,
        openBank: feeSegment.openAccountBank
      };
    }

    // 准备 requirementContent - 包含文件和费用信息
    const contentData: any = {};
    if (lotDoc && lotDoc.bidDocuments && lotDoc.bidDocuments.length > 0) {
      contentData.bidDocuments = lotDoc.bidDocuments;
    }
    if (cnDocumentForm.bidFeeCollection === true && feeSegment) {
      contentData.bidFeeInfo = {
        amount: feeSegment.bidFeeAmount,
        payBank: feeSegment.payBank,
        payAccount: feeSegment.payAccount,
        openAccountBank: feeSegment.openAccountBank
      };
    }

    // 创建标段对象
    bidsSegments.push({
      requirementType: 'BID_DOCUMENT',
      sectionId: sectionId,
      requirementName: `${lot.sectionName}标书文件`,
      requirementContent: JSON.stringify(contentData),
      bidFeeInfo: bidFeeInfo,
      attachmentInfos: attachmentInfos.length > 0 ? attachmentInfos : undefined
    });
  });

  // 转换评分细则为 evaluationStandardInfos
  const evaluationStandardInfos: EvaluationStandardInfo[] = [];
  // 检查 scoringRules 是否有数据
  if (cnDocumentForm.scoringRules && cnDocumentForm.scoringRules.length > 0) {
    cnDocumentForm.scoringRules.forEach(lotRule => {
      const sectionId = lotRule.sectionId;

      evaluationStandardInfos.push({
        sectionId: sectionId,
        type: lotRule.type || 'SCORE',
        nodeName: lotRule.nodeName || '',
        itemName: lotRule.itemName || '',
        itemDescription: lotRule.itemDescription || '',
        totalScore: parseFloat(lotRule.totalScore?.toString() || '0'),
        maxScore: parseFloat(lotRule.maxScore?.toString() || '0'),
        minScore: parseFloat(lotRule.minScore?.toString() || '0'),
        weight: lotRule.weight || 0,
        sortOrder: 0,
        isRequired: 1
      });
    });
  }

  return {
    projectId: projectId.value ,
    noticeId: noticeId.value,
    fileSubmissionAddress: cnDocumentForm.deliveryAddress,
    bidFeeCollection: cnDocumentForm.bidFeeCollection,
    bidsSegments: bidsSegments,
    biddingNotice: cnDocumentForm.quotationNotice,
    evaluationMethod: cnDocumentForm.evaluationMethod,
    fileObtainStartTime: timeReq.documentObtainTimeRange[0] || '',
    fileObtainEndTime: timeReq.documentObtainTimeRange[1] || '',
    bidDocPayStartTime: timeReq.bidFeePaymentTimeRange[0] || '',
    bidDocPayEndTime: timeReq.bidFeePaymentTimeRange[1] || '',
    quoteStartTime: timeReq.quotationTimeRange[0] || '',
    quoteEndTime: timeReq.quotationTimeRange[1] || '',
    evaluationStandardInfos: evaluationStandardInfos,
    approvalType: cnDocumentForm.approvalData.approvalType,
    specialProcessExecutorList: cnDocumentForm.approvalData.specialProcessExecutorList
  };
};

// API数据转换为表单数据
const apiDataToFormData = (data: NegotiationDocApiData) => {
  // 直接赋值API数据
  Object.assign(negotiationDocData, data);

  // 同步到表单数据
  Object.assign(cnDocumentForm, {
    deliveryAddress: data.fileSubmissionAddress || '',
    bidFeeCollection: data.bidFeeCollection || false,
    quotationNotice: data.biddingNotice || '',
    evaluationMethod: data.evaluationMethod || '',
    timeRequirements: {
      documentObtainTimeRange: [data.fileObtainStartTime || '', data.fileObtainEndTime || ''],
      bidFeePaymentTimeRange: [data.bidDocPayStartTime || '', data.bidDocPayEndTime || ''],
      quotationTimeRange: [data.quoteStartTime || '', data.quoteEndTime || '']
    },
    approvalData: {
      approvalType: data.approvalType || 0,
      specialProcessExecutorList: data.specialProcessExecutorList || []
    }
  });

  // 反向解析 bidsSegments 到表单数据
  if (data.bidsSegments && data.bidsSegments.length > 0) {
    // 初始化数据结构
    const bidFeeSegmentsMap = new Map<string, BidFeeInfo>();
    const lotBidDocumentsMap = new Map<string, AttachmentInfo[]>();

    // 遍历 bidsSegments 解析数据 - 现在只有 BID_DOCUMENT 类型，包含文件和费用信息
    data.bidsSegments.forEach(segment => {
      const lotId = segment.sectionId;

      if (segment.requirementType === 'BID_DOCUMENT') {
        // 解析标书文件信息
        const existingDocs = lotBidDocumentsMap.get(lotId) || [];

        // 优先从 attachmentInfos 获取文件信息
        if (segment.attachmentInfos && segment.attachmentInfos.length > 0) {
          segment.attachmentInfos.forEach(attachment => {
            if (attachment.fileName && attachment.filePath) {
              existingDocs.push({
                fileName: attachment.fileName,
                filePath: attachment.filePath
              });
            }
          });
        }

        // 从 requirementContent 解析文件和费用信息
        if (segment.requirementContent) {
          try {
            const contentData = JSON.parse(segment.requirementContent);

            // 解析文件信息
            if (contentData.bidDocuments && Array.isArray(contentData.bidDocuments)) {
              contentData.bidDocuments.forEach((doc: any) => {
                if (doc.fileName && doc.filePath) {
                  existingDocs.push({
                    fileName: doc.fileName,
                    filePath: doc.filePath
                  });
                }
              });
            }
            // 兼容旧格式 - 单个文件信息
            else if (contentData.fileName && contentData.filePath) {
              existingDocs.push({
                fileName: contentData.fileName,
                filePath: contentData.filePath
              });
            }

            // 解析费用信息
            if (contentData.bidFeeInfo) {
              const feeInfo = contentData.bidFeeInfo;
              bidFeeSegmentsMap.set(lotId, {
                lotId: lotId,
                bidFeeAmount: feeInfo.amount || '',
                payAccount: feeInfo.payAccount || '',
                payBank: feeInfo.payBank || '',
                openAccountBank: feeInfo.openAccountBank || ''
              });
            }
          } catch (error) {
            console.error('解析标书内容信息失败:', error);
          }
        }

        // 解析标书费信息 - 优先从 bidFeeInfo 字段获取
        if (segment.bidFeeInfo) {
          bidFeeSegmentsMap.set(lotId, {
            lotId: lotId,
            bidFeeAmount: segment.bidFeeInfo.bidFee?.toString() || '',
            payAccount: segment.bidFeeInfo.bankAccount || '',
            payBank: segment.bidFeeInfo.bank || '',
            openAccountBank: segment.bidFeeInfo.openBank || ''
          });
        }

        lotBidDocumentsMap.set(lotId, existingDocs);
      }
    });

    // 更新表单数据中的标书费信息
    cnDocumentForm.bidFeeSegments.forEach(segment => {
      const feeInfo = bidFeeSegmentsMap.get(segment.lotId);
      if (feeInfo) {
        Object.assign(segment, feeInfo);
      }
    });

    // 更新表单数据中的标书文件信息
    cnDocumentForm.lotBidDocuments.forEach(lotDoc => {
      const docs = lotBidDocumentsMap.get(lotDoc.lotId);
      if (docs && docs.length > 0) {
        lotDoc.bidDocuments = docs;
      }
    });

    // 同步到文件上传组件数据 - 确保每个标段都有独立的文件数组
    lotList.value.forEach(lot => {
      const docs = lotBidDocumentsMap.get(lot.id);
      if (docs && docs.length > 0) {
        // 使用深拷贝确保数据独立性
        lotBidDocumentFiles.value[lot.id] = docs.map(doc => ({
          name: doc.fileName,
          url: doc.filePath
        }));
      } else {
        // 确保每个标段都有空数组，避免undefined
        lotBidDocumentFiles.value[lot.id] = [];
      }
    });

    console.log('API数据转换后的文件数据:', JSON.stringify(lotBidDocumentFiles.value));
  }

  // 反向解析 evaluationStandardInfos 到表单数据
  if (data.evaluationStandardInfos && data.evaluationStandardInfos.length > 0) {
    const scoringRulesMap = new Map<string, ScoringRule[]>();

    // 按标段分组评分规则
    data.evaluationStandardInfos.forEach(standard => {
      const lotId = standard.sectionId?.toString();
      const existingRules = scoringRulesMap.get(lotId) || [];

      const scoringRule: ScoringRule = {
        lotId: lotId,
        sectionId: lotId,
        type: standard.type || 'SCORE',
        nodeName: standard.nodeName || '',
        itemName: standard.itemName || '',
        itemDescription: standard.itemDescription || '',
        maxScore: standard.maxScore,
        minScore: standard.minScore,
        weight: standard.weight,
        vetoThreshold: standard.vetoThreshold,
        totalScore: standard.totalScore
      };

      existingRules.push(scoringRule);
      scoringRulesMap.set(lotId, existingRules);
    });

    // 将所有标段的评分规则合并到表单数据
    const allScoringRules: ScoringRule[] = [];
    scoringRulesMap.forEach((rules) => {
      allScoringRules.push(...rules);
    });

    console.log(allScoringRules);

    cnDocumentForm.scoringRules = allScoringRules;
  }

};

// 加载现有数据 - 使用新接口
const loadExistingData = async () => {
  const currentNoticeId = noticeId.value;

  if (!currentNoticeId) {
    console.log('缺少noticeId，无法加载数据');
    return;
  }

  try {
    let response = null;
    if (props.mode === 'change') { // 变更-文件详情
      const {data} = await getNoticeChangeDetailInfo({
        noticeId: currentNoticeId,
        projectId: projectId.value,
        changeTypeEnum: 'CHANGE_BID_DOC',
      });
      response = data
    } else {
      await biddingStore.initCndDocument()
      response = cloneDeep(cndDocumentInfo.value)
    }
    apiDataToFormData(response);
  } catch (error) {
    console.log('加载竞谈文件数据失败:', error);
    // 不显示错误消息，因为可能是第一次创建
  }
};

// 处理审批类型变化
const handleApprovalTypeChange = (value: number) => {
  cnDocumentForm.approvalData.approvalType = value;
  if (value === 0) {
    // 系统自动发起时清空审批人列表
    cnDocumentForm.approvalData.specialProcessExecutorList = [];
  }
};

// 处理审批数据变化
const handleApprovalDataChange = (userIds: string[]) => {
  console.log('UserSelector update:modelValue event:', userIds);
  // update:modelValue 事件直接返回 userId 数组
  if (Array.isArray(userIds)) {
    cnDocumentForm.approvalData.specialProcessExecutorList = userIds;
  } else {
    cnDocumentForm.approvalData.specialProcessExecutorList = [];
  }
  console.log('Processed approval data:', cnDocumentForm.approvalData.specialProcessExecutorList);
};

// 表单提交
const handleSubmit = async () => {
  if (!mainFormRef.value) return;

  try {
    await mainFormRef.value.validate();
    btnLoading.value = true;

    // 使用正确的数据组装函数
    const negotiationApiData = formToNegotiationApiData();
    console.log('提交数据:', negotiationApiData);
    console.log('bidsSegments 长度:', negotiationApiData.bidsSegments?.length || 0);
    console.log('evaluationStandardInfos 长度:', negotiationApiData.evaluationStandardInfos?.length || 0);

    await upsertNegotiationDoc(negotiationApiData);
    ElMessage.success('竞谈文件保存成功');
    router.back()
    // 触发成功事件，通知父组件
    emits('success', negotiationApiData);
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    btnLoading.value = false;
  }
};

const fileData = computed(() => {
  return formToNegotiationApiData();
})

// 取消操作
const handleCancel = () => {
  // 重置表单或返回上一页
  ElMessage.info('已取消操作');
  emits('cancel');
};

// 监听全局标书费收取方式变化
watch(() => cnDocumentForm.bidFeeCollection, (newValue) => {
  if (!newValue) {
    // 如果不收取标书费，清空标书费缴纳时间和所有标段的标书费信息
    cnDocumentForm.timeRequirements.bidFeePaymentTimeRange = ['', ''];
    cnDocumentForm.bidFeeSegments.forEach(segment => {
      segment.bidFeeAmount = '';
      segment.payAccount = '';
      segment.payBank = '';
      segment.openAccountBank = '';
    });

    // 清除标书费缴纳时间的验证错误
    nextTick(() => {
      if (mainFormRef.value) {
        mainFormRef.value.clearValidate('timeRequirements.bidFeePaymentTimeRange');
      }
    });
  }
});

// 监听活动标段变化，确保文件数据正确同步
watch(() => activeBidFeeLotId.value, (newLotId, oldLotId) => {
  if (oldLotId && newLotId !== oldLotId) {
    // 确保新标段有独立的文件数组
    if (!lotBidDocumentFiles.value[newLotId]) {
      lotBidDocumentFiles.value[newLotId] = [];
    }
  }
});

// 定义事件
const emits = defineEmits<{
  success: [data: CNDocumentApiData | NegotiationDocApiData];
  cancel: [];
}>();

defineExpose({
  mainFormRef,
  fileData,
});

// 组件挂载时初始化
onMounted(async () => {
  initializeLotData();
  updateSliderPosition();

  // 设置默认的评分细则标段
  if (lotList.value.length > 0) {
    activeScoringLotId.value = lotList.value[0].id;
  }

  // 加载现有数据
  await loadExistingData();

  // 监听滚动事件，更新当前激活的锚点
  const handleScroll = () => {
    const sections = anchorList.value.map(anchor => ({
      id: anchor.id,
      element: document.getElementById(`section-${anchor.id}`)
    })).filter(section => section.element);

    let currentSection = sections[0]?.id;

    for (const section of sections) {
      const rect = section.element!.getBoundingClientRect();
      if (rect.top <= 100) {
        currentSection = section.id;
      }
    }

    if (currentSection !== activeAnchor.value) {
      activeAnchor.value = currentSection;
      updateSliderPosition();
    }
  };

  window.addEventListener('scroll', handleScroll);

  // 组件卸载时清理事件监听
  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
  });
});

</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

.procurement-project-container {
  display: flex;
  gap: 20px;
  padding: 20px;

  // 响应式布局：小屏幕时改为垂直布局
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  // 左侧锚点导航
  .anchor-navigation {
    width: 120px; // 恢复固定宽度
    flex-shrink: 0; // 防止在空间不足时被压缩
    position: sticky;
    border-left: 1px solid #e6eaf0;
    top: 20px;
    height: fit-content;

    // 小屏幕时的样式调整
    @media (max-width: 768px) {
      width: 100%;
      position: relative;
      border-left: none;
      border-bottom: 1px solid #e6eaf0;
      padding-bottom: 12px;
      margin-bottom: 12px;

      .nav-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .nav-slider {
          display: none; // 小屏幕时隐藏滑块
        }

        .nav-item {
          padding: 4px 8px;
          border: 1px solid #e6eaf0;
          border-radius: 4px;
          font-size: 11px;

          &.active {
            border-color: var(--Color-Primary-color-primary, #0069ff);
            background-color: rgba(0, 105, 255, 0.1);
          }
        }
      }
    }

    .nav-list {
      list-style: none;
      padding: 0;
      margin: 0;
      position: relative; // 为滑块定位提供上下文

      // 高亮滑块
      .nav-slider {
        position: absolute;
        left: -1px;
        width: 1px;
        height: 16px;
        background-color: #0069ff;
        transition: transform 0.2s ease-out;
        will-change: transform;
        z-index: 1;

        // 淡入效果
        &[v-show] {
          transition: transform 0.2s ease-out, opacity 0.3s ease;
        }
      }

      .nav-item {
        padding: 2px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s;
        color: var(--Light-Light-el-text-color-primary, #1c2026);
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        position: relative; // 确保层级关系

        &.active {
          color: var(--Color-Primary-color-primary, #0069ff);
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }
      }
    }
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    min-width: 0; // 关键！防止内容溢出，确保能够收缩
    padding-right: 20px;

    @media (max-width: 768px) {
      padding-right: 0;
    }

    // 确保内部表单元素也能正确自适应
    .section-form {
      // 响应式表单布局
      .el-row {
        @media (max-width: 768px) {
          .el-col {
            &:not(:last-child) {
              margin-bottom: 16px;
            }
          }
        }

        @media (max-width: 576px) {
          .el-col {
            &[span='12'],
            &[span='8'] {
              span: 24 !important; // 强制全宽
            }
          }
        }
      }

      // 表格容器响应式
      .el-table {
        @media (max-width: 768px) {
          font-size: 12px;
        }
      }

      // 输入框和选择器响应式
      .el-input,
      .el-select,
      .el-date-picker {
        @media (max-width: 576px) {
          width: 100% !important;
        }
      }
    }

    // 全局响应式处理
    @media (max-width: 768px) {
      // 强制表单列变为单列布局
      :deep(.el-col) {
        &.el-col-12,
        &.el-col-8 {
          max-width: 100% !important;
          flex: 0 0 100% !important;
        }
      }

      // 表格在小屏幕上的优化
      :deep(.el-table) {
        .el-table__body-wrapper {
          overflow-x: auto;
        }
      }

      // 折叠面板优化
      :deep(.el-collapse) {
        .el-collapse-item__header {
          padding: 12px 16px;
          font-size: 14px;
        }

        .el-collapse-item__content {
          padding: 16px;
        }
      }
    }

    // 超小屏幕优化
    @media (max-width: 576px) {
      :deep(.el-form-item) {
        margin-bottom: 16px;

        .el-form-item__label {
          font-size: 12px;
          padding-bottom: 4px;
        }

        .el-form-item__content {
          .el-input__inner,
          .el-textarea__inner {
            font-size: 14px;
          }
        }
      }

      // 表格操作按钮响应式
      :deep(.table-actions) {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

// 条件说明
.conditions-notice {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

// 上传区域
.upload-section {
  display: flex;
  align-items: center;
  gap: 12px;

  span {
    font-size: 14px;
    color: #606266;
  }
}

// 标段信息样式
.bid-sections-container {
  .bid-section-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    .section-item {
      flex: 1;

      .section-label {
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
        color: #606266;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 2px;
        }
      }

      .section-name-input,
      .section-number-input {
        width: 100%;
      }
    }

    .section-actions {
      flex-shrink: 0;

      .delete-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .add-section-btn-container {
    margin-top: 16px;
    text-align: left;

    .add-section-btn {
      border: 1px dashed #409eff;
      background-color: #fff;
      color: #409eff;

      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

.form-item-tip {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

// 审批设置样式
.approval-section {
  padding: 16px 0;

  .el-form-item {
    margin-bottom: 0;
  }

  .flex {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  @media (max-width: 576px) {
    .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
}
</style>
